#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <signal.h>

int main(int argc, char *argv[]) {
    pid_t pid;
    int status;
    
    // 检查命令行参数
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <test_program> [args...]\n", argv[0]);
        exit(1);
    }
    
    printf("Parent process (PID: %d) starting to fork\n", getpid());
    
    // 创建子进程
    pid = fork();
    
    if (pid == -1) {
        perror("fork failed");
        exit(1);
    }
    else if (pid == 0) {
        // 子进程
        printf("Child process (PID: %d) starting to execute test program: %s\n", 
               getpid(), argv[1]);
        
        // 准备参数数组给execve
        char **args = malloc((argc) * sizeof(char*));
        for (int i = 1; i < argc; i++) {
            args[i-1] = argv[i];
        }
        args[argc-1] = NULL;
        
        // 执行测试程序
        execve(argv[1], args, NULL);
        
        // 如果execve返回，说明出错了
        perror("execve failed");
        free(args);
        exit(EXIT_FAILURE);
    }
    else {
        // 父进程
        printf("Parent process (PID: %d) waiting for child process (PID: %d)\n", 
               getpid(), pid);
        
        // 等待子进程结束并获取状态
        pid_t waited_pid = wait(&status);
        
        if (waited_pid == -1) {
            perror("wait failed");
            exit(1);
        }
        
        printf("\nChild process termination information:\n");
        printf("Child PID: %d\n", waited_pid);
        
        // 分析子进程的终止状态
        if (WIFEXITED(status)) {
            // 正常终止
            int exit_status = WEXITSTATUS(status);
            printf("Normal termination with exit status: %d\n", exit_status);
        }
        else if (WIFSIGNALED(status)) {
            // 被信号终止
            int term_signal = WTERMSIG(status);
            printf("Terminated by signal: %d (%s)\n", term_signal, strsignal(term_signal));
            
            // 检查是否产生了core dump
            if (WCOREDUMP(status)) {
                printf("Core dump generated\n");
            }
        }
        else if (WIFSTOPPED(status)) {
            // 被信号停止（这种情况在wait()中通常不会发生，除非使用WUNTRACED）
            int stop_signal = WSTOPSIG(status);
            printf("Stopped by signal: %d (%s)\n", stop_signal, strsignal(stop_signal));
        }
        else {
            printf("Unknown termination status\n");
        }
        
        printf("Parent process exiting\n");
    }
    
    return 0;
}
